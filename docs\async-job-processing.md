# Async Job Processing

## Overview

The crawler application implements asynchronous job processing to handle long-running crawl operations without blocking event handler threads. This prevents resource exhaustion and ensures the system remains responsive.

## Architecture

### Components

1. **AsyncJobProcessor**: Manages worker pool and job execution
2. **JobStartEventHandler**: Queues jobs for async processing
3. **EventHandlerManager**: Coordinates event handling and async processing
4. **Job Commands**: Update job status and progress

### Flow Diagram

```
Job Start Event → Event Handler → Async Job Processor → Worker Pool
                                        ↓
                                   Crawl Service
                                        ↓
                                 Job Status Updates
```

## Key Features

### 1. Non-Blocking Event Processing

**Problem**: Synchronous crawl operations block Kafka consumer threads
```go
// ❌ Blocking approach (old)
func (h *JobStartEventHandler) Handle(ctx context.Context, evt event.Event) error {
    // This blocks the event handler thread for hours
    err := h.crawlService.CrawlPriceNormal(ctx, config)
    return err
}
```

**Solution**: Asynchronous job queuing
```go
// ✅ Non-blocking approach (new)
func (h *JobStartEventHandler) Handle(ctx context.Context, evt event.Event) error {
    // This returns immediately after queuing
    err := h.asyncJobProcessor.ProcessCrawlJobAsync(ctx, jobID, configID)
    return err
}
```

### 2. Worker Pool Management

- **Configurable Concurrency**: Limit concurrent crawl jobs (default: 5)
- **Resource Protection**: Prevents system overload
- **Queue Management**: Jobs wait for available workers

```go
type AsyncJobProcessorConfig struct {
    MaxConcurrentJobs int           // Max concurrent crawl jobs
    JobTimeout        time.Duration // Timeout per job (default: 2 hours)
}
```

### 3. Job Lifecycle Management

#### Job States
- `queued`: Job is waiting for worker
- `running`: Job is actively crawling
- `completed`: Job finished successfully
- `failed`: Job encountered error
- `cancelled`: Job was manually cancelled

#### Progress Tracking
```go
// Update job progress during crawl
h.updateJobProgress(ctx, jobID, currentPage, totalPages, "Crawling page 5/20")
```

### 4. Graceful Shutdown

- **Active Job Tracking**: Monitor all running jobs
- **Cancellation Support**: Cancel jobs during shutdown
- **Timeout Handling**: Wait for jobs to complete or timeout

## Usage Examples

### 1. Starting a Crawl Job

```go
// Job start event is published
jobStartedEvent := event.NewJobStartedEvent("job-123", event.JobStartedData{
    JobID:   123,
    JobType: "crawl",
    Name:    "Daily Price Crawl",
})

// Event handler queues job asynchronously
// Returns immediately, crawl happens in background
```

### 2. Monitoring Active Jobs

```go
// Get currently running jobs
activeJobs := asyncJobProcessor.GetActiveJobs()

for jobID, execution := range activeJobs {
    fmt.Printf("Job %d: running for %v\n", 
        jobID, time.Since(execution.StartTime))
}
```

### 3. Cancelling a Job

```go
// Cancel a running job
err := asyncJobProcessor.CancelJob(jobID)
if err != nil {
    log.Printf("Failed to cancel job %d: %v", jobID, err)
}
```

## Configuration

### Environment Variables

```bash
# Maximum concurrent crawl jobs
CRAWLER_MAX_CONCURRENT_JOBS=5

# Job timeout in hours
CRAWLER_JOB_TIMEOUT_HOURS=2

# Worker pool buffer size
CRAWLER_WORKER_POOL_SIZE=10
```

### Application Configuration

```go
config := service.AsyncJobProcessorConfig{
    MaxConcurrentJobs: 5,
    JobTimeout:        2 * time.Hour,
}

processor := service.NewAsyncJobProcessor(
    crawlService,
    crawlConfigQueries,
    jobQueries,
    jobCommands,
    broker,
    logger,
    config,
)
```

## Error Handling

### 1. Worker Pool Exhaustion

```go
err := processor.ProcessCrawlJobAsync(ctx, jobID, configID)
if err != nil {
    // Handle case where no workers are available
    // Job will be retried later or marked as failed
}
```

### 2. Job Timeout

```go
// Jobs automatically timeout after configured duration
// Status is updated to "failed" with timeout error
```

### 3. Crawl Service Errors

```go
// Errors during crawl are captured and job is marked as failed
// Error details are stored in job record for debugging
```

## Monitoring and Observability

### 1. Metrics

- `active_jobs_count`: Number of currently running jobs
- `job_queue_size`: Number of jobs waiting for workers
- `job_completion_rate`: Jobs completed per minute
- `job_failure_rate`: Jobs failed per minute

### 2. Logging

```go
// Structured logging for job lifecycle
logger.Info("Job queued for async processing", 
    "job_id", jobID, 
    "config_id", configID)

logger.Info("Job started", 
    "job_id", jobID, 
    "worker_id", workerID)

logger.Info("Job completed", 
    "job_id", jobID, 
    "duration", duration)
```

### 3. Health Checks

```go
// Check if async processor is healthy
func (p *AsyncJobProcessor) HealthCheck() error {
    if len(p.activeJobs) > p.maxConcurrentJobs {
        return errors.New("too many active jobs")
    }
    return nil
}
```

## Best Practices

### 1. Resource Management

- Set appropriate worker pool size based on system resources
- Monitor memory usage during concurrent crawls
- Implement circuit breakers for external services

### 2. Error Recovery

- Implement retry logic for transient failures
- Store detailed error information for debugging
- Use exponential backoff for retries

### 3. Performance Optimization

- Batch job status updates to reduce database load
- Use connection pooling for database operations
- Implement job prioritization if needed

### 4. Testing

```go
// Test async job processing
func TestAsyncJobProcessing(t *testing.T) {
    processor := setupTestProcessor()
    
    // Queue job
    err := processor.ProcessCrawlJobAsync(ctx, 123, 456)
    assert.NoError(t, err)
    
    // Wait for completion
    eventually(func() bool {
        activeJobs := processor.GetActiveJobs()
        return len(activeJobs) == 0
    }, 30*time.Second)
    
    // Verify job completed
    job := getJobFromDB(123)
    assert.Equal(t, "completed", job.Status)
}
```

## Troubleshooting

### Common Issues

1. **Jobs stuck in queue**: Check worker pool configuration
2. **High memory usage**: Reduce concurrent job limit
3. **Jobs timing out**: Increase timeout or optimize crawl logic
4. **Event handler blocking**: Verify async processing is enabled

### Debug Commands

```bash
# Check active jobs
curl http://localhost:8080/api/jobs/active

# Cancel stuck job
curl -X POST http://localhost:8080/api/jobs/123/cancel

# View job logs
kubectl logs -f crawler-pod | grep "job_id=123"
```

## Migration from Synchronous Processing

### Before (Blocking)
```go
// Event handler blocks for entire crawl duration
func (h *JobStartEventHandler) Handle(ctx context.Context, evt event.Event) error {
    return h.crawlService.CrawlPriceNormal(ctx, config) // Blocks for hours
}
```

### After (Non-blocking)
```go
// Event handler returns immediately after queuing
func (h *JobStartEventHandler) Handle(ctx context.Context, evt event.Event) error {
    return h.asyncJobProcessor.ProcessCrawlJobAsync(ctx, jobID, configID) // Returns immediately
}
```

This migration ensures the system remains responsive and can handle multiple concurrent operations efficiently.
