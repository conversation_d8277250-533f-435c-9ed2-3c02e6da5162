package event

import (
	"fmt"
	"time"
)

type Event interface {
	GetID() string
	GetType() string
	GetTimestamp() time.Time
	GetEventVersion() string
	GetAggregateID() string
	GetData() interface{}
	Marshal() ([]byte, error)
}

type EventBase struct {
	EventID      string    `json:"event_id"`
	EventType    string    `json:"event_type"`
	EventVersion string    `json:"event_version"`
	Timestamp    time.Time `json:"timestamp"`
	AggregateID  string    `json:"aggregate_id"`
}

func (e *EventBase) GetID() string           { return e.EventID }
func (e *EventBase) GetType() string         { return e.EventType }
func (e *EventBase) GetTimestamp() time.Time { return e.Timestamp }
func (e *EventBase) GetAggregateID() string  { return e.AggregateID }
func (e *EventBase) GetEventVersion() string { return e.EventVersion }

// Helper function to generate event IDs
func generateEventID() string {
	return fmt.Sprintf("evt-%d", time.Now().UnixNano())
}
