package event

import (
	"encoding/json"
	"fmt"
	"time"
)

type Event interface {
	GetID() string
	GetType() string
	GetTimestamp() time.Time
	GetEventVersion() string
	GetAggregateID() string
	GetData() interface{}
	Marshal() ([]byte, error)
}

type EventBase struct {
	EventID      string    `json:"event_id"`
	EventType    string    `json:"event_type"`
	EventVersion string    `json:"event_version"`
	Timestamp    time.Time `json:"timestamp"`
	AggregateID  string    `json:"aggregate_id"`
}

func (e *EventBase) GetID() string           { return e.EventID }
func (e *EventBase) GetType() string         { return e.EventType }
func (e *EventBase) GetTimestamp() time.Time { return e.Timestamp }
func (e *EventBase) GetAggregateID() string  { return e.AggregateID }
func (e *EventBase) GetEventVersion() string { return e.EventVersion }

// BaseEvent is a generic event structure that embeds EventBase and holds typed data
type BaseEvent[T any] struct {
	EventBase
	Data T `json:"data"`
}

// GetData returns the typed data payload
func (e *BaseEvent[T]) GetData() interface{} {
	return e.Data
}

// Marshal serializes the event to JSON
func (e *BaseEvent[T]) Marshal() ([]byte, error) {
	return json.Marshal(e)
}

// NewBaseEvent creates a new BaseEvent with the given type, aggregate ID, and data
func NewBaseEvent[T any](eventType, aggregateID string, data T) *BaseEvent[T] {
	return &BaseEvent[T]{
		EventBase: EventBase{
			EventID:      generateEventID(),
			EventType:    eventType,
			EventVersion: "1.0",
			Timestamp:    time.Now().UTC(),
			AggregateID:  aggregateID,
		},
		Data: data,
	}
}

// Helper function to generate event IDs
func generateEventID() string {
	return fmt.Sprintf("evt-%d", time.Now().UnixNano())
}
