package commandjob

import (
	"context"
	"fmt"

	"go_core_market/internal/crawler/app/service"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
)

// CancelAsyncJob command to cancel a running async job
type CancelAsyncJob struct {
	JobID       int
	CancelledBy string
	Reason      string
}

type CancelAsyncJob<PERSON><PERSON>ler decorator.CommandHandler[CancelAsyncJob]

type cancelAs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> struct {
	asyncJobProcessor *service.AsyncJobProcessor
}

func NewCancelAsyncJobHandler(
	asyncJobProcessor *service.AsyncJobProcessor,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
) CancelAsyncJobHandler {
	if asyncJobProcessor == nil {
		panic("nil asyncJobProcessor")
	}

	return decorator.ApplyCommandDecorators[CancelAsyncJob](
		cancelAsyncJobHandler{asyncJobProcessor: asyncJobProcessor},
		logger,
		metricsClient,
	)
}

func (h cancelAsyncJobHandler) Handle(ctx context.Context, cmd CancelAsyncJob) error {
	// Cancel the async job
	err := h.asyncJobProcessor.CancelJob(cmd.JobID)
	if err != nil {
		return fmt.Errorf("failed to cancel async job %d: %w", cmd.JobID, err)
	}

	return nil
}
