package event

import (
	"time"
)

// ====== Example Usage Functions ======

// CreateJobCreatedEventExample demonstrates how to create a JobCreatedEvent
func CreateJobCreatedEventExample() *JobCreatedEvent {
	aggregateID := "job-12345"
	jobID := 12345
	jobType := "crawl"
	name := "Crawl Market Prices"
	description := "Crawl prices from market for analysis"
	crawlConfigID := 1
	maxRetries := 3
	timeout := 300 // 5 minutes
	createdBy := "user-admin"
	scheduledAt := time.Now().Add(1 * time.Hour) // Schedule for 1 hour later

	return NewJobCreatedEvent(
		aggregateID,
		jobID,
		jobType,
		name,
		description,
		crawlConfigID,
		maxRetries,
		timeout,
		createdBy,
		&scheduledAt,
	)
}

// CreateJobStartedEventExample demonstrates how to create a JobStartedEvent
func CreateJobStartedEventExample() *JobStartedEvent {
	aggregateID := "job-12345"
	jobID := 12345
	jobType := "crawl"
	name := "Crawl Market Prices"
	startedBy := "crawler-service"

	return NewJobStartedEvent(
		aggregateID,
		jobID,
		jobType,
		name,
		startedBy,
	)
}

// CreateJobProgressUpdatedEventExample demonstrates how to create a JobProgressUpdatedEvent
func CreateJobProgressUpdatedEventExample() *JobProgressUpdatedEvent {
	aggregateID := "job-12345"
	jobID := 12345
	jobType := "crawl"
	name := "Crawl Market Prices"
	
	progress := JobProgressDTO{
		CurrentStep: 2,
		TotalSteps:  5,
		Message:     "Processing market data",
		Percentage:  40,
	}

	return NewJobProgressUpdatedEvent(
		aggregateID,
		jobID,
		jobType,
		name,
		progress,
	)
}

// CreateJobCompletedEventExample demonstrates how to create a JobCompletedEvent
func CreateJobCompletedEventExample() *JobCompletedEvent {
	aggregateID := "job-12345"
	jobID := 12345
	jobType := "crawl"
	name := "Crawl Market Prices"
	duration := 5 * time.Minute
	
	progress := JobProgressDTO{
		CurrentStep: 5,
		TotalSteps:  5,
		Message:     "Job completed successfully",
		Percentage:  100,
	}
	
	results := map[string]interface{}{
		"total_items_crawled": 1500,
		"success_rate":        98.5,
		"errors":              []string{},
		"output_file":         "/data/crawl_results_12345.json",
	}

	return NewJobCompletedEvent(
		aggregateID,
		jobID,
		jobType,
		name,
		duration,
		progress,
		results,
	)
}

// CreateJobFailedEventExample demonstrates how to create a JobFailedEvent
func CreateJobFailedEventExample() *JobFailedEvent {
	aggregateID := "job-12345"
	jobID := 12345
	jobType := "crawl"
	name := "Crawl Market Prices"
	errorMessage := "Connection timeout to market API"
	retryCount := 2
	maxRetries := 3
	canRetry := true
	
	progress := JobProgressDTO{
		CurrentStep: 3,
		TotalSteps:  5,
		Message:     "Failed at data processing step",
		Percentage:  60,
	}

	return NewJobFailedEvent(
		aggregateID,
		jobID,
		jobType,
		name,
		errorMessage,
		retryCount,
		maxRetries,
		canRetry,
		progress,
	)
}

// CreateJobCancelledEventExample demonstrates how to create a JobCancelledEvent
func CreateJobCancelledEventExample() *JobCancelledEvent {
	aggregateID := "job-12345"
	jobID := 12345
	jobType := "crawl"
	name := "Crawl Market Prices"
	cancelledBy := "user-admin"
	reason := "Market maintenance detected"
	
	progress := JobProgressDTO{
		CurrentStep: 2,
		TotalSteps:  5,
		Message:     "Job cancelled by user",
		Percentage:  40,
	}

	return NewJobCancelledEvent(
		aggregateID,
		jobID,
		jobType,
		name,
		cancelledBy,
		reason,
		progress,
	)
}

// CreateJobRetriedEventExample demonstrates how to create a JobRetriedEvent
func CreateJobRetriedEventExample() *JobRetriedEvent {
	aggregateID := "job-12345"
	jobID := 12345
	jobType := "crawl"
	name := "Crawl Market Prices"
	previousError := "Network timeout"
	retryCount := 1
	maxRetries := 3
	
	progress := JobProgressDTO{
		CurrentStep: 2,
		TotalSteps:  5,
		Message:     "Retrying after network error",
		Percentage:  40,
	}

	return NewJobRetriedEvent(
		aggregateID,
		jobID,
		jobType,
		name,
		previousError,
		retryCount,
		maxRetries,
		progress,
	)
}

// CreateJobPausedEventExample demonstrates how to create a JobPausedEvent
func CreateJobPausedEventExample() *JobPausedEvent {
	aggregateID := "job-12345"
	jobID := 12345
	jobType := "crawl"
	name := "Crawl Market Prices"
	pausedBy := "user-admin"
	reason := "System maintenance"
	
	progress := JobProgressDTO{
		CurrentStep: 3,
		TotalSteps:  5,
		Message:     "Job paused for maintenance",
		Percentage:  60,
	}

	return NewJobPausedEvent(
		aggregateID,
		jobID,
		jobType,
		name,
		pausedBy,
		reason,
		progress,
	)
}

// CreateJobResumedEventExample demonstrates how to create a JobResumedEvent
func CreateJobResumedEventExample() *JobResumedEvent {
	aggregateID := "job-12345"
	jobID := 12345
	jobType := "crawl"
	name := "Crawl Market Prices"
	resumedBy := "user-admin"
	
	progress := JobProgressDTO{
		CurrentStep: 3,
		TotalSteps:  5,
		Message:     "Job resumed after maintenance",
		Percentage:  60,
	}

	return NewJobResumedEvent(
		aggregateID,
		jobID,
		jobType,
		name,
		resumedBy,
		progress,
	)
}

// CreateJobScheduledEventExample demonstrates how to create a JobScheduledEvent
func CreateJobScheduledEventExample() *JobScheduledEvent {
	aggregateID := "job-12345"
	jobID := 12345
	jobType := "crawl"
	name := "Crawl Market Prices"
	scheduledBy := "scheduler-service"
	scheduledAt := time.Now().Add(2 * time.Hour) // Schedule for 2 hours later

	return NewJobScheduledEvent(
		aggregateID,
		jobID,
		jobType,
		name,
		scheduledBy,
		scheduledAt,
	)
}
