package event

import (
	"context"
	"fmt"
	"log"
	"time"

	"go_core_market/pkg/message"
	"go_core_market/pkg/message/kafka"
	"go_core_market/pkg/message/serializer"
)

// JobEventPublisher demonstrates how to publish job events to Kafka
type JobEventPublisher struct {
	broker message.Broker
}

func NewJobEventPublisher(broker message.Broker) *JobEventPublisher {
	return &JobEventPublisher{
		broker: broker,
	}
}

// PublishJobCreated publishes a job created event
func (p *JobEventPublisher) PublishJobCreated(ctx context.Context, jobID int, jobType, name, description string, crawlConfigID, maxRetries, timeout int, createdBy string, scheduledAt *time.Time) error {
	aggregateID := generateJobAggregateID(jobID)
	
	event := NewJobCreatedEvent(
		aggregateID,
		jobID,
		jobType,
		name,
		description,
		crawlConfigID,
		maxRetries,
		timeout,
		createdBy,
		scheduledAt,
	)

	log.Printf("Publishing JobCreatedEvent for job %d", jobID)
	return p.broker.Publish(ctx, event)
}

// PublishJobStarted publishes a job started event
func (p *JobEventPublisher) PublishJobStarted(ctx context.Context, jobID int, jobType, name, startedBy string) error {
	aggregateID := generateJobAggregateID(jobID)
	
	event := NewJobStartedEvent(
		aggregateID,
		jobID,
		jobType,
		name,
		startedBy,
	)

	log.Printf("Publishing JobStartedEvent for job %d", jobID)
	return p.broker.Publish(ctx, event)
}

// PublishJobProgress publishes a job progress updated event
func (p *JobEventPublisher) PublishJobProgress(ctx context.Context, jobID int, jobType, name string, progress JobProgressDTO) error {
	aggregateID := generateJobAggregateID(jobID)
	
	event := NewJobProgressUpdatedEvent(
		aggregateID,
		jobID,
		jobType,
		name,
		progress,
	)

	log.Printf("Publishing JobProgressUpdatedEvent for job %d - %d%%", jobID, progress.Percentage)
	return p.broker.Publish(ctx, event)
}

// PublishJobCompleted publishes a job completed event
func (p *JobEventPublisher) PublishJobCompleted(ctx context.Context, jobID int, jobType, name string, duration time.Duration, progress JobProgressDTO, results map[string]interface{}) error {
	aggregateID := generateJobAggregateID(jobID)
	
	event := NewJobCompletedEvent(
		aggregateID,
		jobID,
		jobType,
		name,
		duration,
		progress,
		results,
	)

	log.Printf("Publishing JobCompletedEvent for job %d", jobID)
	return p.broker.Publish(ctx, event)
}

// PublishJobFailed publishes a job failed event
func (p *JobEventPublisher) PublishJobFailed(ctx context.Context, jobID int, jobType, name, errorMessage string, retryCount, maxRetries int, canRetry bool, progress JobProgressDTO) error {
	aggregateID := generateJobAggregateID(jobID)
	
	event := NewJobFailedEvent(
		aggregateID,
		jobID,
		jobType,
		name,
		errorMessage,
		retryCount,
		maxRetries,
		canRetry,
		progress,
	)

	log.Printf("Publishing JobFailedEvent for job %d: %s", jobID, errorMessage)
	return p.broker.Publish(ctx, event)
}

// PublishJobCancelled publishes a job cancelled event
func (p *JobEventPublisher) PublishJobCancelled(ctx context.Context, jobID int, jobType, name, cancelledBy, reason string, progress JobProgressDTO) error {
	aggregateID := generateJobAggregateID(jobID)
	
	event := NewJobCancelledEvent(
		aggregateID,
		jobID,
		jobType,
		name,
		cancelledBy,
		reason,
		progress,
	)

	log.Printf("Publishing JobCancelledEvent for job %d: %s", jobID, reason)
	return p.broker.Publish(ctx, event)
}

// Helper function to generate consistent aggregate IDs for jobs
func generateJobAggregateID(jobID int) string {
	return fmt.Sprintf("job-%d", jobID)
}

// SetupJobEventKafkaBroker demonstrates how to set up Kafka broker with job events
func SetupJobEventKafkaBroker() (message.Broker, error) {
	// Create event registry and register job events
	registry := serializer.NewEventRegistry()
	RegisterJobEvents(registry)

	// Create typed serializer
	typedSerializer := serializer.NewTypedSerializer(registry)

	// Configure Kafka broker
	config := kafka.Config{
		Brokers:         []string{"localhost:9092"},
		BatchSize:       100,
		BatchTimeout:    100 * time.Millisecond,
		ReadBatchSize:   100,
		CommitInterval:  1 * time.Second,
		StartOffset:     -1, // Latest
		GroupID:         "job-event-consumer",
		Serializer:      typedSerializer,
		MaxRetries:      3,
		RetryBackoff:    1 * time.Second,
		ReadTimeout:     10 * time.Second,
		WriteTimeout:    10 * time.Second,
		RequiredAcks:    1,
		Compression:     0, // No compression
		MaxMessageBytes: 1000000, // 1MB
	}

	// Create Kafka broker
	broker := kafka.NewKafkaBroker(config)

	return broker, nil
}

// JobEventSubscriber demonstrates how to subscribe to job events
type JobEventSubscriber struct {
	broker message.Broker
}

func NewJobEventSubscriber(broker message.Broker) *JobEventSubscriber {
	return &JobEventSubscriber{
		broker: broker,
	}
}

// SubscribeToJobEvents subscribes to all job events
func (s *JobEventSubscriber) SubscribeToJobEvents(ctx context.Context) error {
	// Subscribe to job topic (all job events go to "job" topic)
	return s.broker.Subscribe(ctx, "job", s.handleJobEvent)
}

// handleJobEvent handles incoming job events
func (s *JobEventSubscriber) handleJobEvent(ctx context.Context, event Event) error {
	log.Printf("Received job event: %s (ID: %s)", event.GetType(), event.GetID())

	switch event.GetType() {
	case JobCreatedEventType:
		return s.handleJobCreated(ctx, event.(*JobCreatedEvent))
	case JobStartedEventType:
		return s.handleJobStarted(ctx, event.(*JobStartedEvent))
	case JobProgressEventType:
		return s.handleJobProgress(ctx, event.(*JobProgressUpdatedEvent))
	case JobCompletedEventType:
		return s.handleJobCompleted(ctx, event.(*JobCompletedEvent))
	case JobFailedEventType:
		return s.handleJobFailed(ctx, event.(*JobFailedEvent))
	case JobCancelledEventType:
		return s.handleJobCancelled(ctx, event.(*JobCancelledEvent))
	case JobPausedEventType:
		return s.handleJobPaused(ctx, event.(*JobPausedEvent))
	case JobResumedEventType:
		return s.handleJobResumed(ctx, event.(*JobResumedEvent))
	case JobRetriedEventType:
		return s.handleJobRetried(ctx, event.(*JobRetriedEvent))
	case JobScheduledEventType:
		return s.handleJobScheduled(ctx, event.(*JobScheduledEvent))
	default:
		log.Printf("Unknown job event type: %s", event.GetType())
		return nil
	}
}

// Event handlers
func (s *JobEventSubscriber) handleJobCreated(ctx context.Context, event *JobCreatedEvent) error {
	log.Printf("Job %d created: %s", event.JobID, event.Name)
	// Add your business logic here
	return nil
}

func (s *JobEventSubscriber) handleJobStarted(ctx context.Context, event *JobStartedEvent) error {
	log.Printf("Job %d started: %s", event.JobID, event.Name)
	// Add your business logic here
	return nil
}

func (s *JobEventSubscriber) handleJobProgress(ctx context.Context, event *JobProgressUpdatedEvent) error {
	log.Printf("Job %d progress: %d%% - %s", event.JobID, event.Progress.Percentage, event.Progress.Message)
	// Add your business logic here
	return nil
}

func (s *JobEventSubscriber) handleJobCompleted(ctx context.Context, event *JobCompletedEvent) error {
	log.Printf("Job %d completed: %s (Duration: %v)", event.JobID, event.Name, event.Duration)
	// Add your business logic here
	return nil
}

func (s *JobEventSubscriber) handleJobFailed(ctx context.Context, event *JobFailedEvent) error {
	log.Printf("Job %d failed: %s (Error: %s)", event.JobID, event.Name, event.ErrorMessage)
	// Add your business logic here
	return nil
}

func (s *JobEventSubscriber) handleJobCancelled(ctx context.Context, event *JobCancelledEvent) error {
	log.Printf("Job %d cancelled: %s (Reason: %s)", event.JobID, event.Name, event.Reason)
	// Add your business logic here
	return nil
}

func (s *JobEventSubscriber) handleJobPaused(ctx context.Context, event *JobPausedEvent) error {
	log.Printf("Job %d paused: %s (Reason: %s)", event.JobID, event.Name, event.Reason)
	// Add your business logic here
	return nil
}

func (s *JobEventSubscriber) handleJobResumed(ctx context.Context, event *JobResumedEvent) error {
	log.Printf("Job %d resumed: %s", event.JobID, event.Name)
	// Add your business logic here
	return nil
}

func (s *JobEventSubscriber) handleJobRetried(ctx context.Context, event *JobRetriedEvent) error {
	log.Printf("Job %d retried: %s (Attempt %d/%d)", event.JobID, event.Name, event.RetryCount, event.MaxRetries)
	// Add your business logic here
	return nil
}

func (s *JobEventSubscriber) handleJobScheduled(ctx context.Context, event *JobScheduledEvent) error {
	log.Printf("Job %d scheduled: %s (At: %v)", event.JobID, event.Name, event.ScheduledAt)
	// Add your business logic here
	return nil
}
