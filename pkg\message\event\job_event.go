package event

import (
	"encoding/json"
	"time"
)

// ====== Event Types Constants ======
const (
	JobCreatedEventType   = "job.created"
	JobScheduledEventType = "job.scheduled"
	JobStartedEventType   = "job.started"
	JobPausedEventType    = "job.paused"
	JobResumedEventType   = "job.resumed"
	JobCompletedEventType = "job.completed"
	JobFailedEventType    = "job.failed"
	JobCancelledEventType = "job.cancelled"
	JobRetriedEventType   = "job.retried"
	JobProgressEventType  = "job.progress_updated"
)

// ====== Job DTOs ======
type JobProgressDTO struct {
	CurrentStep int    `json:"current_step"`
	TotalSteps  int    `json:"total_steps"`
	Message     string `json:"message"`
	Percentage  int    `json:"percentage"`
}

// ====== Job Created Event ======
type JobCreatedEvent struct {
	EventBase
	JobID         int        `json:"job_id"`
	JobType       string     `json:"job_type"` // "crawl", "analysis", "cleanup", "export"
	Name          string     `json:"name"`
	Description   string     `json:"description,omitempty"`
	CrawlConfigID int        `json:"crawl_config_id,omitempty"`
	MaxRetries    int        `json:"max_retries"`
	Timeout       int        `json:"timeout_seconds"`
	CreatedBy     string     `json:"created_by"`
	CreatedAt     time.Time  `json:"created_at"`
	ScheduledAt   *time.Time `json:"scheduled_at,omitempty"`
}

func (e *JobCreatedEvent) Marshal() ([]byte, error) {
	return json.Marshal(e)
}

func NewJobCreatedEvent(aggregateID string) *JobCreatedEvent {

}

// ====== Job Scheduled Event ======
type JobScheduledData struct {
	JobID       int       `json:"job_id"`
	JobType     string    `json:"job_type"`
	Name        string    `json:"name"`
	ScheduledAt time.Time `json:"scheduled_at"`
	ScheduledBy string    `json:"scheduled_by,omitempty"`
}

type JobScheduledEvent struct {
	*BaseEvent[JobScheduledData]
}

func NewJobScheduledEvent(aggregateID string, data JobScheduledData) *JobScheduledEvent {
	return &JobScheduledEvent{
		BaseEvent: NewBaseEvent(JobScheduledEventType, aggregateID, data),
	}
}

// ====== Job Started Event ======
type JobStartedEvent struct {
	EventBase
	JobID     int       `json:"job_id"`
	JobType   string    `json:"job_type"`
	Name      string    `json:"name"`
	StartedAt time.Time `json:"started_at"`
	StartedBy string    `json:"started_by,omitempty"`
}


func NewJobStartedEvent(aggregateID string, ....) *JobStartedEvent {
	return
}

// ====== Job Paused Event ======

// ====== Job Resumed Event ======
type JobResumedData struct {
	JobID     int       `json:"job_id"`
	JobType   string    `json:"job_type"`
	Name      string    `json:"name"`
	ResumedAt time.Time `json:"resumed_at"`
	ResumedBy string    `json:"resumed_by,omitempty"`
}

type JobResumedEvent struct {
	*BaseEvent[JobResumedData]
}

func NewJobResumedEvent(aggregateID string, data JobResumedData) *JobResumedEvent {
	return &JobResumedEvent{
		BaseEvent: NewBaseEvent(JobResumedEventType, aggregateID, data),
	}
}

// ====== Job Completed Event ======
type JobCompletedData struct {
	JobID       int                    `json:"job_id"`
	JobType     string                 `json:"job_type"`
	Name        string                 `json:"name"`
	CompletedAt time.Time              `json:"completed_at"`
	Duration    time.Duration          `json:"duration"`
	Progress    JobProgressDTO         `json:"progress"`
	Results     map[string]interface{} `json:"results,omitempty"` // Optional: job results/output
}

type JobCompletedEvent struct {
	*BaseEvent[JobCompletedData]
}

func NewJobCompletedEvent(aggregateID string, data JobCompletedData) *JobCompletedEvent {
	return &JobCompletedEvent{
		BaseEvent: NewBaseEvent(JobCompletedEventType, aggregateID, data),
	}
}

// ====== Job Failed Event ======
type JobFailedData struct {
	JobID        int            `json:"job_id"`
	JobType      string         `json:"job_type"`
	Name         string         `json:"name"`
	FailedAt     time.Time      `json:"failed_at"`
	ErrorMessage string         `json:"error_message"`
	RetryCount   int            `json:"retry_count"`
	MaxRetries   int            `json:"max_retries"`
	CanRetry     bool           `json:"can_retry"`
	Progress     JobProgressDTO `json:"progress"`
}

type JobFailedEvent struct {
	*BaseEvent[JobFailedData]
}

func NewJobFailedEvent(aggregateID string, data JobFailedData) *JobFailedEvent {
	return &JobFailedEvent{
		BaseEvent: NewBaseEvent(JobFailedEventType, aggregateID, data),
	}
}

// ====== Job Cancelled Event ======
type JobCancelledData struct {
	JobID       int       `json:"job_id"`
	JobType     string    `json:"job_type"`
	Name        string    `json:"name"`
	CancelledAt time.Time `json:"cancelled_at"`
	CancelledBy string    `json:"cancelled_by,omitempty"`
	Reason      string    `json:"reason,omitempty"`
}

type JobCancelledEvent struct {
	*BaseEvent[JobCancelledData]
}

func NewJobCancelledEvent(aggregateID string, data JobCancelledData) *JobCancelledEvent {
	return &JobCancelledEvent{
		BaseEvent: NewBaseEvent(JobCancelledEventType, aggregateID, data),
	}
}

// ====== Job Retried Event ======
type JobRetriedData struct {
	JobID         int       `json:"job_id"`
	JobType       string    `json:"job_type"`
	Name          string    `json:"name"`
	RetriedAt     time.Time `json:"retried_at"`
	RetryCount    int       `json:"retry_count"`
	MaxRetries    int       `json:"max_retries"`
	PreviousError string    `json:"previous_error,omitempty"`
}

type JobRetriedEvent struct {
	*BaseEvent[JobRetriedData]
}

func NewJobRetriedEvent(aggregateID string, data JobRetriedData) *JobRetriedEvent {
	return &JobRetriedEvent{
		BaseEvent: NewBaseEvent(JobRetriedEventType, aggregateID, data),
	}
}

// ====== Job Progress Updated Event ======
type JobProgressUpdatedData struct {
	JobID     int            `json:"job_id"`
	JobType   string         `json:"job_type"`
	Name      string         `json:"name"`
	Progress  JobProgressDTO `json:"progress"`
	UpdatedAt time.Time      `json:"updated_at"`
}

type JobProgressUpdatedEvent struct {
	*BaseEvent[JobProgressUpdatedData]
}

func NewJobProgressUpdatedEvent(aggregateID string, data JobProgressUpdatedData) *JobProgressUpdatedEvent {
	return &JobProgressUpdatedEvent{
		BaseEvent: NewBaseEvent(JobProgressEventType, aggregateID, data),
	}
}
