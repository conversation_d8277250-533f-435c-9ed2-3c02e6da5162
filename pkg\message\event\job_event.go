package event

import (
	"encoding/json"
	"time"
)

// ====== Event Types Constants ======
const (
	JobCreatedEventType   = "job.created"
	JobScheduledEventType = "job.scheduled"
	JobStartedEventType   = "job.started"
	JobPausedEventType    = "job.paused"
	JobResumedEventType   = "job.resumed"
	JobCompletedEventType = "job.completed"
	JobFailedEventType    = "job.failed"
	JobCancelledEventType = "job.cancelled"
	JobRetriedEventType   = "job.retried"
	JobProgressEventType  = "job.progress_updated"
)

// ====== Job DTOs ======
type JobProgressDTO struct {
	CurrentStep int    `json:"current_step"`
	TotalSteps  int    `json:"total_steps"`
	Message     string `json:"message"`
	Percentage  int    `json:"percentage"`
}

// ====== Job Created Event ======
type JobCreatedEvent struct {
	EventBase
	JobID         int        `json:"job_id"`
	JobType       string     `json:"job_type"` // "crawl", "analysis", "cleanup", "export"
	Name          string     `json:"name"`
	Description   string     `json:"description,omitempty"`
	CrawlConfigID int        `json:"crawl_config_id,omitempty"`
	MaxRetries    int        `json:"max_retries"`
	Timeout       int        `json:"timeout_seconds"`
	CreatedBy     string     `json:"created_by"`
	CreatedAt     time.Time  `json:"created_at"`
	ScheduledAt   *time.Time `json:"scheduled_at,omitempty"`
}

func (e *JobCreatedEvent) GetData() interface{} {
	return map[string]interface{}{
		"job_id":           e.JobID,
		"job_type":         e.JobType,
		"name":             e.Name,
		"description":      e.Description,
		"crawl_config_id":  e.CrawlConfigID,
		"max_retries":      e.MaxRetries,
		"timeout_seconds":  e.Timeout,
		"created_by":       e.CreatedBy,
		"created_at":       e.CreatedAt,
		"scheduled_at":     e.ScheduledAt,
	}
}

func (e *JobCreatedEvent) Marshal() ([]byte, error) {
	return json.Marshal(e)
}

func NewJobCreatedEvent(aggregateID string, jobID int, jobType, name, description string, crawlConfigID, maxRetries, timeout int, createdBy string, scheduledAt *time.Time) *JobCreatedEvent {
	return &JobCreatedEvent{
		EventBase: EventBase{
			EventID:      generateEventID(),
			EventType:    JobCreatedEventType,
			EventVersion: "1.0",
			Timestamp:    time.Now().UTC(),
			AggregateID:  aggregateID,
		},
		JobID:         jobID,
		JobType:       jobType,
		Name:          name,
		Description:   description,
		CrawlConfigID: crawlConfigID,
		MaxRetries:    maxRetries,
		Timeout:       timeout,
		CreatedBy:     createdBy,
		CreatedAt:     time.Now().UTC(),
		ScheduledAt:   scheduledAt,
	}
}

// ====== Job Scheduled Event ======
type JobScheduledEvent struct {
	EventBase
	JobID       int       `json:"job_id"`
	JobType     string    `json:"job_type"`
	Name        string    `json:"name"`
	ScheduledAt time.Time `json:"scheduled_at"`
	ScheduledBy string    `json:"scheduled_by,omitempty"`
}

func (e *JobScheduledEvent) GetData() interface{} {
	return map[string]interface{}{
		"job_id":       e.JobID,
		"job_type":     e.JobType,
		"name":         e.Name,
		"scheduled_at": e.ScheduledAt,
		"scheduled_by": e.ScheduledBy,
	}
}

func (e *JobScheduledEvent) Marshal() ([]byte, error) {
	return json.Marshal(e)
}

func NewJobScheduledEvent(aggregateID string, jobID int, jobType, name, scheduledBy string, scheduledAt time.Time) *JobScheduledEvent {
	return &JobScheduledEvent{
		EventBase: EventBase{
			EventID:      generateEventID(),
			EventType:    JobScheduledEventType,
			EventVersion: "1.0",
			Timestamp:    time.Now().UTC(),
			AggregateID:  aggregateID,
		},
		JobID:       jobID,
		JobType:     jobType,
		Name:        name,
		ScheduledAt: scheduledAt,
		ScheduledBy: scheduledBy,
	}
}

// ====== Job Started Event ======
type JobStartedEvent struct {
	EventBase
	JobID     int       `json:"job_id"`
	JobType   string    `json:"job_type"`
	Name      string    `json:"name"`
	StartedAt time.Time `json:"started_at"`
	StartedBy string    `json:"started_by,omitempty"`
}

func (e *JobStartedEvent) GetData() interface{} {
	return map[string]interface{}{
		"job_id":     e.JobID,
		"job_type":   e.JobType,
		"name":       e.Name,
		"started_at": e.StartedAt,
		"started_by": e.StartedBy,
	}
}

func (e *JobStartedEvent) Marshal() ([]byte, error) {
	return json.Marshal(e)
}

func NewJobStartedEvent(aggregateID string, jobID int, jobType, name, startedBy string) *JobStartedEvent {
	return &JobStartedEvent{
		EventBase: EventBase{
			EventID:      generateEventID(),
			EventType:    JobStartedEventType,
			EventVersion: "1.0",
			Timestamp:    time.Now().UTC(),
			AggregateID:  aggregateID,
		},
		JobID:     jobID,
		JobType:   jobType,
		Name:      name,
		StartedAt: time.Now().UTC(),
		StartedBy: startedBy,
	}
}

// ====== Job Paused Event ======
type JobPausedEvent struct {
	EventBase
	JobID     int       `json:"job_id"`
	JobType   string    `json:"job_type"`
	Name      string    `json:"name"`
	PausedAt  time.Time `json:"paused_at"`
	PausedBy  string    `json:"paused_by,omitempty"`
	Reason    string    `json:"reason,omitempty"`
	Progress  JobProgressDTO `json:"progress"`
}

func (e *JobPausedEvent) GetData() interface{} {
	return map[string]interface{}{
		"job_id":    e.JobID,
		"job_type":  e.JobType,
		"name":      e.Name,
		"paused_at": e.PausedAt,
		"paused_by": e.PausedBy,
		"reason":    e.Reason,
		"progress":  e.Progress,
	}
}

func (e *JobPausedEvent) Marshal() ([]byte, error) {
	return json.Marshal(e)
}

func NewJobPausedEvent(aggregateID string, jobID int, jobType, name, pausedBy, reason string, progress JobProgressDTO) *JobPausedEvent {
	return &JobPausedEvent{
		EventBase: EventBase{
			EventID:      generateEventID(),
			EventType:    JobPausedEventType,
			EventVersion: "1.0",
			Timestamp:    time.Now().UTC(),
			AggregateID:  aggregateID,
		},
		JobID:    jobID,
		JobType:  jobType,
		Name:     name,
		PausedAt: time.Now().UTC(),
		PausedBy: pausedBy,
		Reason:   reason,
		Progress: progress,
	}
}

// ====== Job Resumed Event ======
type JobResumedEvent struct {
	EventBase
	JobID     int       `json:"job_id"`
	JobType   string    `json:"job_type"`
	Name      string    `json:"name"`
	ResumedAt time.Time `json:"resumed_at"`
	ResumedBy string    `json:"resumed_by,omitempty"`
	Progress  JobProgressDTO `json:"progress"`
}

func (e *JobResumedEvent) GetData() interface{} {
	return map[string]interface{}{
		"job_id":     e.JobID,
		"job_type":   e.JobType,
		"name":       e.Name,
		"resumed_at": e.ResumedAt,
		"resumed_by": e.ResumedBy,
		"progress":   e.Progress,
	}
}

func (e *JobResumedEvent) Marshal() ([]byte, error) {
	return json.Marshal(e)
}

func NewJobResumedEvent(aggregateID string, jobID int, jobType, name, resumedBy string, progress JobProgressDTO) *JobResumedEvent {
	return &JobResumedEvent{
		EventBase: EventBase{
			EventID:      generateEventID(),
			EventType:    JobResumedEventType,
			EventVersion: "1.0",
			Timestamp:    time.Now().UTC(),
			AggregateID:  aggregateID,
		},
		JobID:     jobID,
		JobType:   jobType,
		Name:      name,
		ResumedAt: time.Now().UTC(),
		ResumedBy: resumedBy,
		Progress:  progress,
	}
}

// ====== Job Completed Event ======
type JobCompletedEvent struct {
	EventBase
	JobID       int                    `json:"job_id"`
	JobType     string                 `json:"job_type"`
	Name        string                 `json:"name"`
	CompletedAt time.Time              `json:"completed_at"`
	Duration    time.Duration          `json:"duration"`
	Progress    JobProgressDTO         `json:"progress"`
	Results     map[string]interface{} `json:"results,omitempty"` // Optional: job results/output
}

func (e *JobCompletedEvent) GetData() interface{} {
	return map[string]interface{}{
		"job_id":       e.JobID,
		"job_type":     e.JobType,
		"name":         e.Name,
		"completed_at": e.CompletedAt,
		"duration":     e.Duration,
		"progress":     e.Progress,
		"results":      e.Results,
	}
}

func (e *JobCompletedEvent) Marshal() ([]byte, error) {
	return json.Marshal(e)
}

func NewJobCompletedEvent(aggregateID string, jobID int, jobType, name string, duration time.Duration, progress JobProgressDTO, results map[string]interface{}) *JobCompletedEvent {
	return &JobCompletedEvent{
		EventBase: EventBase{
			EventID:      generateEventID(),
			EventType:    JobCompletedEventType,
			EventVersion: "1.0",
			Timestamp:    time.Now().UTC(),
			AggregateID:  aggregateID,
		},
		JobID:       jobID,
		JobType:     jobType,
		Name:        name,
		CompletedAt: time.Now().UTC(),
		Duration:    duration,
		Progress:    progress,
		Results:     results,
	}
}

// ====== Job Failed Event ======
type JobFailedEvent struct {
	EventBase
	JobID        int            `json:"job_id"`
	JobType      string         `json:"job_type"`
	Name         string         `json:"name"`
	FailedAt     time.Time      `json:"failed_at"`
	ErrorMessage string         `json:"error_message"`
	RetryCount   int            `json:"retry_count"`
	MaxRetries   int            `json:"max_retries"`
	CanRetry     bool           `json:"can_retry"`
	Progress     JobProgressDTO `json:"progress"`
}

func (e *JobFailedEvent) GetData() interface{} {
	return map[string]interface{}{
		"job_id":        e.JobID,
		"job_type":      e.JobType,
		"name":          e.Name,
		"failed_at":     e.FailedAt,
		"error_message": e.ErrorMessage,
		"retry_count":   e.RetryCount,
		"max_retries":   e.MaxRetries,
		"can_retry":     e.CanRetry,
		"progress":      e.Progress,
	}
}

func (e *JobFailedEvent) Marshal() ([]byte, error) {
	return json.Marshal(e)
}

func NewJobFailedEvent(aggregateID string, jobID int, jobType, name, errorMessage string, retryCount, maxRetries int, canRetry bool, progress JobProgressDTO) *JobFailedEvent {
	return &JobFailedEvent{
		EventBase: EventBase{
			EventID:      generateEventID(),
			EventType:    JobFailedEventType,
			EventVersion: "1.0",
			Timestamp:    time.Now().UTC(),
			AggregateID:  aggregateID,
		},
		JobID:        jobID,
		JobType:      jobType,
		Name:         name,
		FailedAt:     time.Now().UTC(),
		ErrorMessage: errorMessage,
		RetryCount:   retryCount,
		MaxRetries:   maxRetries,
		CanRetry:     canRetry,
		Progress:     progress,
	}
}

// ====== Job Cancelled Event ======
type JobCancelledEvent struct {
	EventBase
	JobID       int       `json:"job_id"`
	JobType     string    `json:"job_type"`
	Name        string    `json:"name"`
	CancelledAt time.Time `json:"cancelled_at"`
	CancelledBy string    `json:"cancelled_by,omitempty"`
	Reason      string    `json:"reason,omitempty"`
	Progress    JobProgressDTO `json:"progress"`
}

func (e *JobCancelledEvent) GetData() interface{} {
	return map[string]interface{}{
		"job_id":       e.JobID,
		"job_type":     e.JobType,
		"name":         e.Name,
		"cancelled_at": e.CancelledAt,
		"cancelled_by": e.CancelledBy,
		"reason":       e.Reason,
		"progress":     e.Progress,
	}
}

func (e *JobCancelledEvent) Marshal() ([]byte, error) {
	return json.Marshal(e)
}

func NewJobCancelledEvent(aggregateID string, jobID int, jobType, name, cancelledBy, reason string, progress JobProgressDTO) *JobCancelledEvent {
	return &JobCancelledEvent{
		EventBase: EventBase{
			EventID:      generateEventID(),
			EventType:    JobCancelledEventType,
			EventVersion: "1.0",
			Timestamp:    time.Now().UTC(),
			AggregateID:  aggregateID,
		},
		JobID:       jobID,
		JobType:     jobType,
		Name:        name,
		CancelledAt: time.Now().UTC(),
		CancelledBy: cancelledBy,
		Reason:      reason,
		Progress:    progress,
	}
}

// ====== Job Retried Event ======
type JobRetriedEvent struct {
	EventBase
	JobID         int       `json:"job_id"`
	JobType       string    `json:"job_type"`
	Name          string    `json:"name"`
	RetriedAt     time.Time `json:"retried_at"`
	RetryCount    int       `json:"retry_count"`
	MaxRetries    int       `json:"max_retries"`
	PreviousError string    `json:"previous_error,omitempty"`
	Progress      JobProgressDTO `json:"progress"`
}

func (e *JobRetriedEvent) GetData() interface{} {
	return map[string]interface{}{
		"job_id":         e.JobID,
		"job_type":       e.JobType,
		"name":           e.Name,
		"retried_at":     e.RetriedAt,
		"retry_count":    e.RetryCount,
		"max_retries":    e.MaxRetries,
		"previous_error": e.PreviousError,
		"progress":       e.Progress,
	}
}

func (e *JobRetriedEvent) Marshal() ([]byte, error) {
	return json.Marshal(e)
}

func NewJobRetriedEvent(aggregateID string, jobID int, jobType, name, previousError string, retryCount, maxRetries int, progress JobProgressDTO) *JobRetriedEvent {
	return &JobRetriedEvent{
		EventBase: EventBase{
			EventID:      generateEventID(),
			EventType:    JobRetriedEventType,
			EventVersion: "1.0",
			Timestamp:    time.Now().UTC(),
			AggregateID:  aggregateID,
		},
		JobID:         jobID,
		JobType:       jobType,
		Name:          name,
		RetriedAt:     time.Now().UTC(),
		RetryCount:    retryCount,
		MaxRetries:    maxRetries,
		PreviousError: previousError,
		Progress:      progress,
	}
}

// ====== Job Progress Updated Event ======
type JobProgressUpdatedEvent struct {
	EventBase
	JobID     int            `json:"job_id"`
	JobType   string         `json:"job_type"`
	Name      string         `json:"name"`
	Progress  JobProgressDTO `json:"progress"`
	UpdatedAt time.Time      `json:"updated_at"`
}

func (e *JobProgressUpdatedEvent) GetData() interface{} {
	return map[string]interface{}{
		"job_id":     e.JobID,
		"job_type":   e.JobType,
		"name":       e.Name,
		"progress":   e.Progress,
		"updated_at": e.UpdatedAt,
	}
}

func (e *JobProgressUpdatedEvent) Marshal() ([]byte, error) {
	return json.Marshal(e)
}

func NewJobProgressUpdatedEvent(aggregateID string, jobID int, jobType, name string, progress JobProgressDTO) *JobProgressUpdatedEvent {
	return &JobProgressUpdatedEvent{
		EventBase: EventBase{
			EventID:      generateEventID(),
			EventType:    JobProgressEventType,
			EventVersion: "1.0",
			Timestamp:    time.Now().UTC(),
			AggregateID:  aggregateID,
		},
		JobID:     jobID,
		JobType:   jobType,
		Name:      name,
		Progress:  progress,
		UpdatedAt: time.Now().UTC(),
	}
}

// ====== Event Registry Helper ======
// RegisterJobEvents registers all job events with the event registry
func RegisterJobEvents(registry interface {
	RegisterEvent(eventType string, eventInstance Event)
}) {
	// Register all job event types
	registry.RegisterEvent(JobCreatedEventType, &JobCreatedEvent{})
	registry.RegisterEvent(JobScheduledEventType, &JobScheduledEvent{})
	registry.RegisterEvent(JobStartedEventType, &JobStartedEvent{})
	registry.RegisterEvent(JobPausedEventType, &JobPausedEvent{})
	registry.RegisterEvent(JobResumedEventType, &JobResumedEvent{})
	registry.RegisterEvent(JobCompletedEventType, &JobCompletedEvent{})
	registry.RegisterEvent(JobFailedEventType, &JobFailedEvent{})
	registry.RegisterEvent(JobCancelledEventType, &JobCancelledEvent{})
	registry.RegisterEvent(JobRetriedEventType, &JobRetriedEvent{})
	registry.RegisterEvent(JobProgressEventType, &JobProgressUpdatedEvent{})
}
