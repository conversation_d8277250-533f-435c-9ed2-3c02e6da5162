package queryjob

import (
	"context"
	"time"

	"go_core_market/internal/crawler/app/service"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
)

// GetActiveJobsQuery query to get currently active/running jobs
type GetActiveJobsQuery struct {
	// No parameters needed - returns all active jobs
}

// ActiveJobInfo represents information about an active job
type ActiveJobInfo struct {
	JobID     int       `json:"job_id"`
	StartTime time.Time `json:"start_time"`
	Status    string    `json:"status"`
	Duration  string    `json:"duration"`
}

// GetActiveJobsResult result containing active jobs
type GetActiveJobsResult struct {
	ActiveJobs []ActiveJobInfo `json:"active_jobs"`
	TotalCount int             `json:"total_count"`
}

type GetActiveJobsHandler decorator.QueryHandler[GetActiveJobsQuery, GetActiveJobsResult]

type getActiveJobsHandler struct {
	asyncJobProcessor *service.AsyncJobProcessor
}

func NewGetActiveJobsHandler(
	asyncJobProcessor *service.AsyncJobProcessor,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
) GetActiveJobsHandler {
	if asyncJobProcessor == nil {
		panic("nil asyncJobProcessor")
	}

	return decorator.ApplyQueryDecorators[GetActiveJobsQuery, GetActiveJobsResult](
		getActiveJobsHandler{asyncJobProcessor: asyncJobProcessor},
		logger,
		metricsClient,
	)
}

func (h getActiveJobsHandler) Handle(ctx context.Context, query GetActiveJobsQuery) (GetActiveJobsResult, error) {
	// Get active jobs from async processor
	activeJobs := h.asyncJobProcessor.GetActiveJobs()
	
	// Convert to result format
	var jobInfos []ActiveJobInfo
	for jobID, execution := range activeJobs {
		duration := time.Since(execution.StartTime)
		
		jobInfo := ActiveJobInfo{
			JobID:     jobID,
			StartTime: execution.StartTime,
			Status:    execution.Status,
			Duration:  duration.String(),
		}
		jobInfos = append(jobInfos, jobInfo)
	}
	
	return GetActiveJobsResult{
		ActiveJobs: jobInfos,
		TotalCount: len(jobInfos),
	}, nil
}
