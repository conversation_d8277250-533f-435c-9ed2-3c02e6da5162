package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	commandjob "go_core_market/internal/crawler/app/command/job"
	querycrawlconfig "go_core_market/internal/crawler/app/query/crawlconfig"
	queryjob "go_core_market/internal/crawler/app/query/job"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

// AsyncJobProcessor handles long-running jobs asynchronously
type AsyncJobProcessor struct {
	crawlService       CrawlService
	crawlConfigQueries *querycrawlconfig.CrawlConfigQueries
	jobQueries         *queryjob.JobQueries
	jobCommands        *commandjob.JobCommands
	broker             message.Broker
	logger             logger.Logger

	// Worker pool management
	workerPool    chan struct{}
	activeJobs    map[int]*JobExecution
	activeJobsMux sync.RWMutex
	shutdownChan  chan struct{}
	wg            sync.WaitGroup
}

// JobExecution represents an active job execution
type JobExecution struct {
	JobID     int
	StartTime time.Time
	Cancel    context.CancelFunc
	Status    string
}

// AsyncJobProcessorConfig configuration for the processor
type AsyncJobProcessorConfig struct {
	MaxConcurrentJobs int
	JobTimeout        time.Duration
}

// NewAsyncJobProcessor creates a new async job processor
func NewAsyncJobProcessor(
	crawlService CrawlService,
	crawlConfigQueries *querycrawlconfig.CrawlConfigQueries,
	jobQueries *queryjob.JobQueries,
	jobCommands *commandjob.JobCommands,
	broker message.Broker,
	logger logger.Logger,
	config AsyncJobProcessorConfig,
) *AsyncJobProcessor {
	if config.MaxConcurrentJobs <= 0 {
		config.MaxConcurrentJobs = 10 // Default
	}
	if config.JobTimeout <= 0 {
		config.JobTimeout = 2 * time.Hour // Default 2 hours
	}

	return &AsyncJobProcessor{
		crawlService:       crawlService,
		crawlConfigQueries: crawlConfigQueries,
		jobQueries:         jobQueries,
		jobCommands:        jobCommands,
		broker:             broker,
		logger:             logger,
		workerPool:         make(chan struct{}, config.MaxConcurrentJobs),
		activeJobs:         make(map[int]*JobExecution),
		shutdownChan:       make(chan struct{}),
	}
}

// ProcessCrawlJobAsync processes a crawl job asynchronously
func (p *AsyncJobProcessor) ProcessCrawlJobAsync(ctx context.Context, jobID int, configID int) error {
	p.logger.Info("Queuing crawl job for async processing",
		"job_id", jobID,
		"config_id", configID)

	// Check if job is already running
	p.activeJobsMux.RLock()
	if _, exists := p.activeJobs[jobID]; exists {
		p.activeJobsMux.RUnlock()
		return fmt.Errorf("job %d is already running", jobID)
	}
	p.activeJobsMux.RUnlock()

	// Try to acquire worker slot (non-blocking)
	select {
	case p.workerPool <- struct{}{}:
		// Got a worker slot, start processing
		p.wg.Add(1)
		go p.processCrawlJob(ctx, jobID, configID)
		return nil
	default:
		// No available workers
		return fmt.Errorf("no available workers for job %d, max concurrent jobs: %d",
			jobID, cap(p.workerPool))
	}
}

// processCrawlJob processes a single crawl job
func (p *AsyncJobProcessor) processCrawlJob(parentCtx context.Context, jobID int, configID int) {
	defer func() {
		// Release worker slot
		<-p.workerPool
		p.wg.Done()

		// Remove from active jobs
		p.activeJobsMux.Lock()
		delete(p.activeJobs, jobID)
		p.activeJobsMux.Unlock()
	}()

	// Create job-specific context with timeout
	ctx, cancel := context.WithTimeout(parentCtx, 2*time.Hour)
	defer cancel()

	// Track active job
	execution := &JobExecution{
		JobID:     jobID,
		StartTime: time.Now(),
		Cancel:    cancel,
		Status:    "running",
	}

	p.activeJobsMux.Lock()
	p.activeJobs[jobID] = execution
	p.activeJobsMux.Unlock()

	p.logger.Info("Starting async crawl job processing",
		"job_id", jobID,
		"config_id", configID)

	// Update job status to running
	err := p.updateJobStatus(ctx, jobID, "running")
	if err != nil {
		p.logger.Error("Failed to update job status to running",
			"job_id", jobID, "error", err)
		return
	}

	// Get crawl config
	config, err := p.crawlConfigQueries.GetConfigByID.Handle(ctx, querycrawlconfig.GetConfigIDQuery{
		ConfigID: configID,
	})
	if err != nil {
		p.logger.Error("Failed to get crawl config",
			"job_id", jobID, "config_id", configID, "error", err)
		p.failJob(ctx, jobID, fmt.Sprintf("Failed to get crawl config: %v", err))
		return
	}

	// Verify config type is normal
	if config.TypeConfig != string(entity.TypeConfigNormal) {
		p.logger.Warn("Config is not normal type, completing job without crawling",
			"job_id", jobID, "config_type", config.TypeConfig)
		p.completeJob(ctx, jobID)
		return
	}

	// Execute crawl with progress tracking
	err = p.executeCrawlWithProgress(ctx, jobID, config)
	if err != nil {
		p.logger.Error("Crawl job failed",
			"job_id", jobID, "config_id", configID, "error", err)
		p.failJob(ctx, jobID, err.Error())
		return
	}

	// Complete job
	p.logger.Info("Crawl job completed successfully",
		"job_id", jobID, "config_id", configID)
	p.completeJob(ctx, jobID)
}

// executeCrawlWithProgress executes crawl with progress updates
func (p *AsyncJobProcessor) executeCrawlWithProgress(ctx context.Context, jobID int, config *querycrawlconfig.CrawlConfig) error {
	// Update progress: Starting crawl
	p.updateJobProgress(ctx, jobID, 0, 100, "Starting crawl...")

	// Execute crawl
	err := p.crawlService.CrawlPriceNormal(ctx, config)
	if err != nil {
		return err
	}

	// Update progress: Completed
	p.updateJobProgress(ctx, jobID, 100, 100, "Crawl completed successfully")
	return nil
}

// updateJobStatus updates job status
func (p *AsyncJobProcessor) updateJobStatus(ctx context.Context, jobID int, status string) error {
	return p.jobCommands.UpdateJobStatus.Handle(ctx, commandjob.UpdateJobStatus{
		ID:     jobID,
		Status: status,
	})
}

// updateJobProgress updates job progress
func (p *AsyncJobProcessor) updateJobProgress(ctx context.Context, jobID int, current, total int, message string) {
	percentage := 0
	if total > 0 {
		percentage = (current * 100) / total
	}

	err := p.jobCommands.UpdateJobProgress.Handle(ctx, commandjob.UpdateJobProgress{
		ID:          jobID,
		CurrentStep: current,
		TotalSteps:  total,
		Message:     message,
		Percentage:  percentage,
	})
	if err != nil {
		p.logger.Error("Failed to update job progress",
			"job_id", jobID, "error", err)
	}
}

// completeJob marks job as completed
func (p *AsyncJobProcessor) completeJob(ctx context.Context, jobID int) {
	err := p.jobCommands.CompleteJob.Handle(ctx, commandjob.CompleteJob{
		ID:          jobID,
		CompletedBy: "async-processor",
	})
	if err != nil {
		p.logger.Error("Failed to complete job", "job_id", jobID, "error", err)
	}
}

// failJob marks job as failed
func (p *AsyncJobProcessor) failJob(ctx context.Context, jobID int, errorMessage string) {
	err := p.jobCommands.FailJob.Handle(ctx, commandjob.FailJob{
		ID:           jobID,
		ErrorMessage: errorMessage,
		FailedBy:     "async-processor",
	})
	if err != nil {
		p.logger.Error("Failed to mark job as failed", "job_id", jobID, "error", err)
	}
}

// GetActiveJobs returns currently active jobs
func (p *AsyncJobProcessor) GetActiveJobs() map[int]*JobExecution {
	p.activeJobsMux.RLock()
	defer p.activeJobsMux.RUnlock()

	result := make(map[int]*JobExecution)
	for k, v := range p.activeJobs {
		result[k] = v
	}
	return result
}

// CancelJob cancels a running job
func (p *AsyncJobProcessor) CancelJob(jobID int) error {
	p.activeJobsMux.RLock()
	execution, exists := p.activeJobs[jobID]
	p.activeJobsMux.RUnlock()

	if !exists {
		return fmt.Errorf("job %d is not running", jobID)
	}

	execution.Cancel()
	p.logger.Info("Job cancellation requested", "job_id", jobID)
	return nil
}

// Shutdown gracefully shuts down the processor
func (p *AsyncJobProcessor) Shutdown(ctx context.Context) error {
	p.logger.Info("Shutting down async job processor")

	close(p.shutdownChan)

	// Cancel all active jobs
	p.activeJobsMux.RLock()
	for jobID, execution := range p.activeJobs {
		p.logger.Info("Cancelling active job during shutdown", "job_id", jobID)
		execution.Cancel()
	}
	p.activeJobsMux.RUnlock()

	// Wait for all workers to finish with timeout
	done := make(chan struct{})
	go func() {
		p.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		p.logger.Info("All async jobs completed")
	case <-ctx.Done():
		p.logger.Warn("Shutdown timeout reached, some jobs may still be running")
	}

	return nil
}
