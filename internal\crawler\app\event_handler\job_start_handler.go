package eventhandler

import (
	"context"
	"fmt"
	querycrawlconfig "go_core_market/internal/crawler/app/query/crawlconfig"
	queryjob "go_core_market/internal/crawler/app/query/job"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/service"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message/event"
)

// JobStartEventHandler handles job start events
type JobStartEventHandler struct {
	asyncJobProcessor  *service.AsyncJobProcessor
	crawlConfigQueries *querycrawlconfig.CrawlConfigQueries
	jobQueries         *queryjob.JobQueries
	logger             logger.Logger
}

// NewJobStartEventHandler creates a new job start event handler
func NewJobStartEventHandler(
	asyncJobProcessor *service.AsyncJobProcessor,
	crawlConfigQueries *querycrawlconfig.CrawlConfigQueries,
	jobQueries *queryjob.JobQueries,
	logger logger.Logger,
) *JobStartEventHandler {
	return &JobStartEventHandler{
		asyncJobProcessor:  asyncJobProcessor,
		crawlConfigQueries: crawlConfigQueries,
		jobQueries:         jobQueries,
		logger:             logger,
	}
}

// Handle processes job start events
func (h *JobStartEventHandler) Handle(ctx context.Context, evt event.JobStartedEvent) error {
	h.logger.Info("Received job start event", "event_id", evt.GetID(), "event_type", evt.GetType())
	data := evt.GetData()
	h.logger.Info("Processing job start",
		"job_id", data.JobID,
		"job_type", data.JobType,
		"job_name", data.Name)

	// Check if job type is crawl
	if data.JobType != string(entity.JobTypeCrawl) {
		h.logger.Debug("Job is not a crawl job, skipping",
			"job_type", data.JobType,
			"job_id", data.JobID)
		return nil
	}

	// Get job details to find crawl config ID
	// Note: We need to get the job details from the job repository to get the crawl config ID
	// Since the event data doesn't include crawl config ID, we need to query it
	// For now, we'll assume the job name or some other mechanism provides the config ID
	// This is a limitation that should be addressed by including crawl_config_id in the event data

	h.logger.Info("Job is a crawl job, processing crawl request", "job_id", data.JobID)

	// Get job details to find crawl config ID
	job, err := h.jobQueries.GetJobById.Handle(ctx, queryjob.GetJobByIdQuery{
		Id: data.JobID,
	})
	if err != nil {
		return fmt.Errorf("failed to get job details for job %d: %w", data.JobID, err)
	}

	// Check if job has crawl config ID
	if job.CrawlConfigId == nil {
		h.logger.Debug("Crawl job has no associated crawl config, skipping", "job_id", data.JobID)
		return nil
	}

	return h.processCrawlJob(ctx, data.JobID, *job.CrawlConfigId)
}

// processCrawlJob handles the crawl job processing asynchronously
func (h *JobStartEventHandler) processCrawlJob(ctx context.Context, jobID int, configID int) error {
	h.logger.Info("Queuing crawl job for async processing", "job_id", jobID, "config_id", configID)

	// Queue job for async processing (non-blocking)
	err := h.asyncJobProcessor.ProcessCrawlJobAsync(ctx, jobID, configID)
	if err != nil {
		return fmt.Errorf("failed to queue crawl job %d for async processing: %w", jobID, err)
	}

	h.logger.Info("Crawl job queued successfully", "job_id", jobID, "config_id", configID)
	return nil
}
