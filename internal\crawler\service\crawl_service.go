package service

import (
	"context"
	"fmt"
	queryauthcral "go_core_market/internal/crawler/app/query/authcrawl"
	querycrawlconfig "go_core_market/internal/crawler/app/query/crawlconfig"
	querymarket "go_core_market/internal/crawler/app/query/market"
	queryproxy "go_core_market/internal/crawler/app/query/proxy"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/internal/crawler/domain/value_object"
)

type CrawlService interface {
	CrawlPriceNormal(ctx context.Context, config *querycrawlconfig.CrawlConfig) error
}

type CrawlServiceImpl struct {
	authCrawlQueries queryauthcral.AuthCrawlQueries
	marketQueries    querymarket.MarketQueries
	proxiesQueries   queryproxy.ProxyQueries
	crawlers         map[string]repository.Crawler
}

func NewCrawlService(
	authCrawlQueries queryauthcral.AuthCrawlQueries,

	marketQueryQueries querymarket.MarketQueries,
	proxiesQueryQueries queryproxy.ProxyQueries,
	crawlers map[string]repository.Crawler,
) CrawlService {
	return &CrawlServiceImpl{
		authCrawlQueries: authCrawlQueries,
		marketQueries:    marketQueryQueries,
		proxiesQueries:   proxiesQueryQueries,
		crawlers:         crawlers,
	}
}

func (s *CrawlServiceImpl) CrawlPriceNormal(ctx context.Context, config *querycrawlconfig.CrawlConfig) error {
	marketQuery := querymarket.GetMarketByIdQuery{Id: config.MarketId}
	market, err := s.marketQueries.GetMarketById.Handle(ctx, marketQuery)
	if err != nil {
		return fmt.Errorf("Get market by id error: %v", err)
	}
	crawler, ok := s.crawlers[market.Name]
	if !ok {
		return fmt.Errorf("can not find crawler")
	}

	var proxies []*queryproxy.Proxy
	if config.MaxNumberProxy > 0 {
		proxyQuery := queryproxy.GetAvailableProxiesQuery{
			Limit: config.MaxNumberProxy,
		}
		proxies, err := s.proxiesQueries.GetAvailableProxies.Handle(ctx, proxyQuery)
		if err != nil {
			return fmt.Errorf("failed to get available proxies: %w", err)
		}
		if len(proxies) == 0 {
			return fmt.Errorf("no available proxies found")
		}
	}

	// 3. Lấy auth credentials nếu cần
	var authCrawls []*queryauthcral.AuthCrawl
	if config.RequireAuth && config.MaxNumberAuth > 0 {
		authQuery := queryauthcral.GetAvailableAuthCrawlByMarketQuery{
			MarketId: config.MarketId,
			Limit:    config.MaxNumberAuth,
		}
		authCrawls, err := s.authCrawlQueries.GetAvailableAuthCrawlByMarket.Handle(ctx, authQuery)
		if err != nil {
			return fmt.Errorf("failed to get available auth crawls: %w", err)
		}
		if len(authCrawls) == 0 {
			return fmt.Errorf("no available auth crawls found for market %d", config.MarketId)
		}
	}
	crawlerConfig := s.buildCrawlerConfig(config, proxies, authCrawls)
	err = crawler.SetConfig(crawlerConfig)
	// 5. Set config cho crawler
	if err != nil {
		return fmt.Errorf("failed to set crawler config: %w", err)
	}

	// 6. Thực hiện crawl price normal
	page := 1
	for {
		prices, totalPages, err := crawler.CrawlNormal(ctx, page)
		if err != nil {
			return fmt.Errorf("failed to crawl normal prices on page %d: %w", page, err)
		}
		fmt.Printf("Crawled %d prices from page %d\n", len(prices), page)
		// TODO: Process and save crawled prices
		// Ở đây bạn có thể thêm logic để lưu prices vào database
		// Kiểm tra xem có còn trang nào không
		if page >= totalPages {
			break
		}
		page++
	}

	return nil
}

// buildCrawlerConfig tạo config cho crawler từ crawl config và resources
func (s *CrawlServiceImpl) buildCrawlerConfig(
	config *querycrawlconfig.CrawlConfig,
	proxies []*queryproxy.Proxy,
	authCrawls []*queryauthcral.AuthCrawl,
) *value_object.Config {
	crawlerConfig := &value_object.Config{
		MarketID:          config.MarketId,
		RequestsPerMinute: config.RequestsPerMinute,
		PerRequestDelay:   config.PerRequestDelaySeconds,
		Timeout:           config.TimeoutSeconds,
		MaxRetries:        config.MaxRetries,
		RetryDelay:        config.RetryDelaySeconds,
	}
	if len(proxies) > 0 {
		crawlerConfig.Proxies = make([]string, len(proxies))
		for i, proxy := range proxies {
			crawlerConfig.Proxies[i] = proxy.ToString()
		}
	}

	// Thêm auth credentials nếu có
	if len(authCrawls) > 0 {
		// Phân loại auth theo type
		var cookies []string
		var apiKeys []string

		for _, auth := range authCrawls {
			switch auth.AuthType {
			case "cookie":
				cookies = append(cookies, auth.Value)
			case "api_key":
				apiKeys = append(apiKeys, auth.Value)
			}
		}
		crawlerConfig.Cookies = cookies
		crawlerConfig.APIKeys = apiKeys
	}

	return crawlerConfig
}
