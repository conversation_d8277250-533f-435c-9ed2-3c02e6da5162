package message

import (
	"context"
	"go_core_market/pkg/message/event"
)

type Message struct {
	Topic       string
	Key         []byte
	Value       []byte
	ContentType string
	Headers     map[string]string
}

type Broker interface {
	Publish(ctx context.Context, evt event.Event[any]) error
	Subscribe(ctx context.Context, topic string, handler EventHandler) error
	Connect(ctx context.Context) error
	Disconnect(ctx context.Context) error
	CloseSubscription(topic string) error
}
type EventHandler func(ctx context.Context, event event.Event[any]) error
