# Job Event System

Hệ thống event cho job management sử dụng struct embedding pattern với EventBase và tích hợp <PERSON> broker.

## Cấu trúc Event

Tất cả job events đều sử dụng struct embedding với `EventBase`:

```go
type JobCreatedEvent struct {
    EventBase
    JobID         int        `json:"job_id"`
    JobType       string     `json:"job_type"`
    Name          string     `json:"name"`
    // ... other fields
}
```

## Các loại Job Events

### 1. JobCreatedEvent
- **Type**: `job.created`
- **Mô tả**: Đ<PERSON><PERSON><PERSON> phát khi một job mới được tạo
- **Fields**: JobID, JobType, Name, Description, CrawlConfigID, MaxRetries, Timeout, CreatedBy, CreatedAt, ScheduledAt

### 2. JobScheduledEvent
- **Type**: `job.scheduled`
- **Mô tả**: <PERSON><PERSON><PERSON><PERSON> phát khi một job được lên lịch
- **Fields**: JobID, JobType, Name, ScheduledAt, ScheduledBy

### 3. JobStartedEvent
- **Type**: `job.started`
- **<PERSON>ô tả**: Đ<PERSON><PERSON>c phát khi một job bắt đầu thực thi
- **Fields**: JobID, JobType, Name, StartedAt, StartedBy

### 4. JobPausedEvent
- **Type**: `job.paused`
- **Mô tả**: Được phát khi một job bị tạm dừng
- **Fields**: JobID, JobType, Name, PausedAt, PausedBy, Reason, Progress

### 5. JobResumedEvent
- **Type**: `job.resumed`
- **Mô tả**: Được phát khi một job được tiếp tục sau khi tạm dừng
- **Fields**: JobID, JobType, Name, ResumedAt, ResumedBy, Progress

### 6. JobProgressUpdatedEvent
- **Type**: `job.progress_updated`
- **Mô tả**: Được phát khi tiến độ job được cập nhật
- **Fields**: JobID, JobType, Name, Progress, UpdatedAt

### 7. JobCompletedEvent
- **Type**: `job.completed`
- **Mô tả**: Được phát khi một job hoàn thành thành công
- **Fields**: JobID, JobType, Name, CompletedAt, Duration, Progress, Results

### 8. JobFailedEvent
- **Type**: `job.failed`
- **Mô tả**: Được phát khi một job thất bại
- **Fields**: JobID, JobType, Name, FailedAt, ErrorMessage, RetryCount, MaxRetries, CanRetry, Progress

### 9. JobCancelledEvent
- **Type**: `job.cancelled`
- **Mô tả**: Được phát khi một job bị hủy
- **Fields**: JobID, JobType, Name, CancelledAt, CancelledBy, Reason, Progress

### 10. JobRetriedEvent
- **Type**: `job.retried`
- **Mô tả**: Được phát khi một job được thử lại sau khi thất bại
- **Fields**: JobID, JobType, Name, RetriedAt, RetryCount, MaxRetries, PreviousError, Progress

## Cách sử dụng

### 1. Tạo Event

```go
// Tạo JobCreatedEvent
event := NewJobCreatedEvent(
    "job-12345",     // aggregateID
    12345,           // jobID
    "crawl",         // jobType
    "Crawl Market Prices", // name
    "Crawl prices from market", // description
    1,               // crawlConfigID
    3,               // maxRetries
    300,             // timeout (seconds)
    "user-admin",    // createdBy
    &scheduledAt,    // scheduledAt (optional)
)
```

### 2. Publish Event với Kafka

```go
// Setup Kafka broker
broker, err := SetupJobEventKafkaBroker()
if err != nil {
    log.Fatal(err)
}

// Connect to Kafka
ctx := context.Background()
err = broker.Connect(ctx)
if err != nil {
    log.Fatal(err)
}

// Create publisher
publisher := NewJobEventPublisher(broker)

// Publish event
err = publisher.PublishJobCreated(ctx, 12345, "crawl", "Crawl Market Prices", "Description", 1, 3, 300, "user-admin", nil)
if err != nil {
    log.Printf("Failed to publish event: %v", err)
}
```

### 3. Subscribe to Events

```go
// Create subscriber
subscriber := NewJobEventSubscriber(broker)

// Subscribe to job events
err = subscriber.SubscribeToJobEvents(ctx)
if err != nil {
    log.Printf("Failed to subscribe: %v", err)
}
```

### 4. Register Events với Event Registry

```go
// Create event registry
registry := serializer.NewEventRegistry()

// Register all job events
RegisterJobEvents(registry)

// Create typed serializer
typedSerializer := serializer.NewTypedSerializer(registry)
```

## JobProgressDTO

Struct để theo dõi tiến độ job:

```go
type JobProgressDTO struct {
    CurrentStep int    `json:"current_step"`
    TotalSteps  int    `json:"total_steps"`
    Message     string `json:"message"`
    Percentage  int    `json:"percentage"`
}
```

## Event Interface Implementation

Mỗi event struct implement các method của Event interface:

- `GetID()`: Trả về event ID
- `GetType()`: Trả về event type
- `GetTimestamp()`: Trả về timestamp
- `GetEventVersion()`: Trả về version
- `GetAggregateID()`: Trả về aggregate ID
- `GetData()`: Trả về data dưới dạng map[string]interface{}
- `Marshal()`: Serialize event thành JSON

## Kafka Topic

Tất cả job events được publish vào topic `job` trong Kafka. Topic được tự động xác định từ event type (phần trước dấu chấm).

## Examples

Xem các file example để biết cách sử dụng chi tiết:
- `job_event_example.go`: Các ví dụ tạo events
- `job_event_kafka_example.go`: Ví dụ tích hợp với Kafka

## Error Handling

- Tất cả functions đều trả về error nếu có lỗi
- Kafka broker có retry mechanism với exponential backoff
- Event serialization/deserialization được handle tự động
